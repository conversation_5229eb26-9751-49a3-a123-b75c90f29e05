import { useState, useEffect, useCallback } from 'react';
import { Task, NewTaskForm } from '../types';
import { api } from '../services/api';

export const useTasks = () => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Transform backend task to frontend format
  const transformTask = (backendTask: any): Task => ({
    ...backendTask,
    // Add frontend compatibility fields
    id: backendTask.task_id,
    completed: backendTask.status === 'completed',
    startTime: backendTask.scheduled_time ? 
      new Date(backendTask.scheduled_time).toLocaleTimeString('en-US', { 
        hour12: false, 
        hour: '2-digit', 
        minute: '2-digit' 
      }) : undefined,
    endTime: backendTask.scheduled_time && backendTask.estimated_duration ? 
      new Date(new Date(backendTask.scheduled_time).getTime() + backendTask.estimated_duration * 60000)
        .toLocaleTimeString('en-US', { 
          hour12: false, 
          hour: '2-digit', 
          minute: '2-digit' 
        }) : undefined,
    isRecurring: backendTask.is_recurring || false,
    isFlexible: backendTask.flexibility > 5,
    notes: backendTask.description || '',
  });

  // Load tasks from API
  const loadTasks = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const backendTasks = await api.tasks.getTasks();
      const transformedTasks = backendTasks.map(transformTask);
      setTasks(transformedTasks);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load tasks');
      console.error('Error loading tasks:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Create a new task
  const createTask = useCallback(async (taskData: NewTaskForm) => {
    setLoading(true);
    setError(null);
    try {
      const result = await api.tasks.createTask(taskData);
      console.log('Task created:', result);
      
      // Reload tasks to get the updated list
      await loadTasks();
      
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create task');
      console.error('Error creating task:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [loadTasks]);

  // Update a task
  const updateTask = useCallback(async (taskId: string, updates: Partial<Task>) => {
    setLoading(true);
    setError(null);
    try {
      await api.tasks.updateTask(taskId, updates);
      
      // Update local state optimistically
      setTasks(prevTasks => 
        prevTasks.map(task => 
          task.task_id === taskId ? { ...task, ...updates } : task
        )
      );
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update task');
      console.error('Error updating task:', err);
      // Reload tasks to revert optimistic update
      await loadTasks();
      throw err;
    } finally {
      setLoading(false);
    }
  }, [loadTasks]);

  // Delete a task
  const deleteTask = useCallback(async (taskId: string) => {
    setLoading(true);
    setError(null);
    try {
      await api.tasks.deleteTask(taskId);
      
      // Remove from local state
      setTasks(prevTasks => prevTasks.filter(task => task.task_id !== taskId));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete task');
      console.error('Error deleting task:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Toggle task completion
  const toggleTaskCompletion = useCallback(async (taskId: string) => {
    const task = tasks.find(t => t.task_id === taskId);
    if (!task) return;

    const newStatus = task.status === 'completed' ? 'planned' : 'completed';
    await updateTask(taskId, { 
      status: newStatus,
      completed: newStatus === 'completed'
    });
  }, [tasks, updateTask]);

  // Schedule a task using AI
  const scheduleTask = useCallback(async (taskId: string, preferredTime?: string) => {
    setLoading(true);
    setError(null);
    try {
      const result = await api.tasks.scheduleTask(taskId, preferredTime);
      console.log('Task scheduled:', result);
      
      // Reload tasks to get updated schedule
      await loadTasks();
      
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to schedule task');
      console.error('Error scheduling task:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [loadTasks]);

  // Load tasks on mount
  useEffect(() => {
    loadTasks();
  }, [loadTasks]);

  return {
    tasks,
    loading,
    error,
    loadTasks,
    createTask,
    updateTask,
    deleteTask,
    toggleTaskCompletion,
    scheduleTask,
  };
};
